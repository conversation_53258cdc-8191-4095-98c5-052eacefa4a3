/*
- function declarations (with associated comments)
- method declarations (with associated comments)
- type specifications
*/
export default `
(
  (comment)* @doc
  .
  (function_declaration
    (identifier) @definition.function) @definition.function
  (#strip! @doc "^//\\s*")
  (#set-adjacent! @doc @definition.function)
)

(
  (comment)* @doc
  .
  (method_declaration
    (field_identifier) @definition.method) @definition.method
  (#strip! @doc "^//\\s*")
  (#set-adjacent! @doc @definition.method)
)

(type_spec
  (type_identifier) @definition.type) @definition.type
`

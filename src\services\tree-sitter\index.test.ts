import { expect } from 'chai';
import sinon from 'sinon';
import * as fs from 'fs/promises';
import * as path from 'path';
import Parser from 'web-tree-sitter';
import { parseSourceCodeForDefinitionsTopLevel } from './index';
import { listFiles } from '@services/glob/list-files';
import { fileExistsAtPath } from '@utils/fs';
import { ClineIgnoreController } from '@core/ignore/ClineIgnoreController';
import { preloadAllLanguages, getCachedLanguage, LANGUAGE_EXTENSIONS } from './languageParser';

// Mock dependencies
let mockListFiles: sinon.SinonStub;
let mockFileExistsAtPath: sinon.SinonStub;
let mockFsReadFile: sinon.SinonStub;
let mockParser: sinon.SinonStub;
let mockPreloadAllLanguages: sinon.SinonStub;
let mockGetCachedLanguage: sinon.SinonStub;

// Mock data
const mockDirPath = '/mock/path';
const mockFiles = [
  path.join(mockDirPath, 'file1.js'),
  path.join(mockDirPath, 'file2.ts'),
  path.join(mockDirPath, 'file3.py')
];
const mockFileContent = 'mock file content';
const mockTree = { rootNode: { startPosition: { row: 0 }, endPosition: { row: 0 } } };
const mockCaptures = [
  { node: { startPosition: { row: 0 }, endPosition: { row: 0 } }, name: 'name.definition.function' }
];
const mockQuery = { captures: sinon.stub().returns(mockCaptures) };
const mockLanguage = { query: sinon.stub().returns(mockQuery) } as unknown as Parser.Language;
const mockCachedLanguage = { language: mockLanguage, query: mockQuery };

// Setup mocks
beforeEach(() => {
  mockListFiles = sinon.stub();
  mockFileExistsAtPath = sinon.stub();
  mockFsReadFile = sinon.stub();
  mockParser = sinon.stub();
  mockPreloadAllLanguages = sinon.stub();
  mockGetCachedLanguage = sinon.stub();

  // Manually set the behavior without using sinon.stub on the actual functions
  mockListFiles.resolves([mockFiles, []]);
  mockFileExistsAtPath.resolves(true);
  mockFsReadFile.resolves(mockFileContent);
  mockParser.returns(mockTree as any);
  mockPreloadAllLanguages.resolves();
  mockGetCachedLanguage.callsFake((ext: string) => {
    if (ext === 'js' || ext === 'ts') {
      return mockCachedLanguage;
    }
    return undefined;
  });
});

// Cleanup after tests
afterEach(() => {
  sinon.restore();
});

describe('parseSourceCodeForDefinitionsTopLevel', () => {
  it('should return error message if directory does not exist', async () => {
    mockFileExistsAtPath.resolves(false);
    const result = await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    expect(result).to.equal('This directory does not exist or you do not have permission to access it.');
  });

  it('should preload all languages to populate cache', async () => {
    await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    expect(mockPreloadAllLanguages.calledOnce).to.be.true;
  });

  it('should use cached language data when available (cache hit)', async () => {
    const result = await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    expect(mockGetCachedLanguage.calledWith('js')).to.be.true;
    expect(mockGetCachedLanguage.calledWith('ts')).to.be.true;
    expect(result).to.contain('file1.js');
    expect(result).to.contain('file2.ts');
  });

  it('should handle cache miss by loading required parsers', async () => {
    mockGetCachedLanguage.withArgs('py').returns(undefined);
    const result = await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    expect(mockGetCachedLanguage.calledWith('py')).to.be.true;
    expect(result).to.contain('file3.py');
  });

  it('should handle errors during file parsing gracefully', async () => {
    mockParser.throws(new Error('Parsing error'));
    const result = await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    expect(result).to.contain('file1.js');
    expect(result).to.contain('file2.ts');
  });

  it('should respect ClineIgnoreController access rules', async () => {
    const mockFilterPaths = sinon.stub().returns([mockFiles[0]]);
    const mockValidateAccess = sinon.stub().returns(true);
    const mockIgnoreController = {
      filterPaths: mockFilterPaths,
      validateAccess: mockValidateAccess
    } as unknown as ClineIgnoreController;
    const result = await parseSourceCodeForDefinitionsTopLevel(mockDirPath, mockIgnoreController);
    expect(mockFilterPaths.calledOnce).to.be.true;
    expect(result).to.contain('file1.js');
    expect(result).not.to.contain('file2.ts');
    expect(result).not.to.contain('file3.py');
  });

  it('should optimize performance by avoiding redundant parser loading', async () => {
    const startTime = Date.now();
    await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    const cacheHitDuration = Date.now() - startTime;

    mockGetCachedLanguage.resetHistory();
    mockGetCachedLanguage.callsFake(() => undefined); // Simulate cache miss for all
    const startTimeMiss = Date.now();
    await parseSourceCodeForDefinitionsTopLevel(mockDirPath);
    const cacheMissDuration = Date.now() - startTimeMiss;

    // Cache hit should be faster than cache miss
    expect(cacheHitDuration).to.be.lessThanOrEqual(cacheMissDuration);
  });
});

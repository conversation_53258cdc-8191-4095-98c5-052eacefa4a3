import * as path from "path"
import Parser from "web-tree-sitter"
import {
	javascriptQuery,
	typescript<PERSON><PERSON>y,
	pythonQuery,
	rustQuery,
	goQuery,
	cppQuery,
	cQuery,
	csharpQ<PERSON>y,
	ruby<PERSON><PERSON>y,
	javaQuery,
	php<PERSON><PERSON>y,
	swift<PERSON><PERSON>y,
	kotlin<PERSON>uery,
} from "./queries"

export interface LanguageParser {
	[key: string]: {
		parser: Parser
		query: Parser.Query
	}
}

export interface CachedParser {
	parser: Parser
	query: Parser.Query
	language: Parser.Language
}

// Global cache for parsers
const parserCache = new Map<string, CachedParser>()
let isParserInitialized = false

async function loadLanguage(langName: string) {
	return await Parser.Language.load(path.join(__dirname, `tree-sitter-${langName}.wasm`))
}

async function initializeParser() {
	if (!isParserInitialized) {
		await Parser.init()
		isParserInitialized = true
	}
}

// Language extension mapping
const LANGUAGE_EXTENSIONS: Record<string, string> = {
	js: "javascript",
	jsx: "javascript",
	ts: "typescript",
	tsx: "typescript", // tsx uses typescript parser
	py: "python",
	rs: "rust",
	go: "go",
	cpp: "cpp",
	cc: "cpp",
	cxx: "cpp",
	hpp: "cpp",
	c: "c",
	h: "c",
	cs: "c_sharp",
	rb: "ruby",
	java: "java",
	php: "php",
	swift: "swift",
	kt: "kotlin",
}

/*
Using node bindings for tree-sitter is problematic in vscode extensions 
because of incompatibility with electron. Going the .wasm route has the 
advantage of not having to build for multiple architectures.

We use web-tree-sitter and tree-sitter-wasms which provides auto-updating prebuilt WASM binaries for tree-sitter's language parsers.

This function loads WASM modules for relevant language parsers based on input files:
1. Extracts unique file extensions
2. Maps extensions to language names
3. Loads corresponding WASM files (containing grammar rules)
4. Uses WASM modules to initialize tree-sitter parsers

This approach optimizes performance by loading only necessary parsers once for all relevant files.

Sources:
- https://github.com/tree-sitter/node-tree-sitter/issues/169
- https://github.com/tree-sitter/node-tree-sitter/issues/168
- https://github.com/Gregoor/tree-sitter-wasms/blob/main/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
*/
export async function loadRequiredLanguageParsers(filesToParse: string[]): Promise<LanguageParser> {
	await initializeParser()
	const extensionsToLoad = new Set(filesToParse.map((file) => path.extname(file).toLowerCase().slice(1)))
	const parsers: LanguageParser = {}
	for (const ext of extensionsToLoad) {
		let language: Parser.Language
		let query: Parser.Query
		switch (ext) {
			case "js":
			case "jsx":
				language = await loadLanguage("javascript")
				query = language.query(javascriptQuery)
				break
			case "ts":
				language = await loadLanguage("typescript")
				query = language.query(typescriptQuery)
				break
			case "tsx":
				language = await loadLanguage("typescript")
				query = language.query(typescriptQuery)
				break
			case "py":
				language = await loadLanguage("python")
				query = language.query(pythonQuery)
				break
			case "rs":
				language = await loadLanguage("rust")
				query = language.query(rustQuery)
				break
			case "go":
				language = await loadLanguage("go")
				query = language.query(goQuery)
				break
			case "cpp":
			case "cc":
			case "cxx":
			case "hpp":
				language = await loadLanguage("cpp")
				query = language.query(cppQuery)
				break
			case "c":
			case "h":
				language = await loadLanguage("c")
				query = language.query(cQuery)
				break
			case "cs":
				language = await loadLanguage("c_sharp")
				query = language.query(csharpQuery)
				break
			case "rb":
				language = await loadLanguage("ruby")
				query = language.query(rubyQuery)
				break
			case "java":
				language = await loadLanguage("java")
				query = language.query(javaQuery)
				break
			case "php":
				language = await loadLanguage("php")
				query = language.query(phpQuery)
				break
			case "swift":
				language = await loadLanguage("swift")
				query = language.query(swiftQuery)
				break
			case "kt":
				language = await loadLanguage("kotlin")
				query = language.query(kotlinQuery)
				break
			default:
				throw new Error(`Unsupported language: ${ext}`)
		}
		const parser = new Parser()
		parser.setLanguage(language)
		parsers[ext] = { parser, query }
	}
	return parsers
}

/**
 * Pre-loads all supported tree-sitter parsers during extension startup.
 * This improves performance by avoiding parser loading during completion requests.
 */
export async function preloadAllParsers(): Promise<void> {
	await initializeParser()

	console.log("🌳 Pre-loading tree-sitter parsers...")

	const supportedExtensions = Object.keys(LANGUAGE_EXTENSIONS)
	const loadPromises = supportedExtensions.map(async (ext) => {
		try {
			const langName = LANGUAGE_EXTENSIONS[ext]

			// Check if already cached
			if (parserCache.has(ext)) {
				return
			}

			const language = await loadLanguage(langName)
			const query = getQueryForLanguage(langName, language)
			const parser = new Parser()
			parser.setLanguage(language)

			parserCache.set(ext, { parser, query, language })
			console.log(`🌳 Loaded parser for .${ext} (${langName})`)
		} catch (error) {
			console.warn(`🌳 Failed to load parser for .${ext}:`, error)
		}
	})

	await Promise.all(loadPromises)
	console.log(`🌳 Pre-loaded ${parserCache.size} tree-sitter parsers`)
}

/**
 * Gets a cached parser for the given file extension.
 * Returns undefined if no parser is available for the extension.
 */
export function getCachedParser(fileExtension: string): CachedParser | undefined {
	const ext = fileExtension.toLowerCase().replace(".", "")
	return parserCache.get(ext)
}

/**
 * Gets a parser for a specific file path.
 * Uses cached parsers when available, falls back to on-demand loading.
 */
export async function getParserForFile(filepath: string): Promise<Parser | undefined> {
	const ext = path.extname(filepath).toLowerCase().slice(1)

	// Try to get from cache first
	const cached = getCachedParser(ext)
	if (cached) {
		// Create a new parser instance to avoid conflicts
		const parser = new Parser()
		parser.setLanguage(cached.language)
		return parser
	}

	// Fall back to on-demand loading for unsupported extensions
	return undefined
}

function getQueryForLanguage(langName: string, language: Parser.Language): Parser.Query {
	switch (langName) {
		case "javascript":
			return language.query(javascriptQuery)
		case "typescript":
			return language.query(typescriptQuery)
		case "python":
			return language.query(pythonQuery)
		case "rust":
			return language.query(rustQuery)
		case "go":
			return language.query(goQuery)
		case "cpp":
			return language.query(cppQuery)
		case "c":
			return language.query(cQuery)
		case "c_sharp":
			return language.query(csharpQuery)
		case "ruby":
			return language.query(rubyQuery)
		case "java":
			return language.query(javaQuery)
		case "php":
			return language.query(phpQuery)
		case "swift":
			return language.query(swiftQuery)
		case "kotlin":
			return language.query(kotlinQuery)
		default:
			throw new Error(`Unsupported language: ${langName}`)
	}
}

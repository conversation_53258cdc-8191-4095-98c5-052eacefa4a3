/**
 * Test file to verify comment extraction functionality
 * This file contains various types of comments and functions to test the enhanced contextExtractor
 */

/**
 * A simple function with JSDoc documentation
 * @param {string} name - The name to greet
 * @param {number} age - The age of the person
 * @returns {string} A greeting message
 */
function greetPerson(name, age) {
    // This is an inline comment
    const message = `Hello ${name}, you are ${age} years old!`;
    return message;
}

// Single line comment before function
// This comment spans multiple lines
// and should be captured as well
function calculateSum(a, b) {
    /* Block comment inside function */
    return a + b;
}

/*
 * Multi-line block comment
 * describing the next function
 * with detailed explanation
 */
function complexCalculation(x, y, z) {
    // Calculate intermediate result
    const intermediate = x * y;
    
    /*
     * Final calculation with
     * multiple steps
     */
    const result = intermediate + z;
    return result;
}

/**
 * Class with JSDoc documentation
 * @class Calculator
 */
class Calculator {
    /**
     * Constructor for Calculator
     * @param {string} name - Calculator name
     */
    constructor(name) {
        this.name = name;
    }
    
    /**
     * Adds two numbers
     * @param {number} a - First number
     * @param {number} b - Second number
     * @returns {number} Sum of a and b
     */
    add(a, b) {
        // Simple addition
        return a + b;
    }
    
    // Method with single line comment
    multiply(a, b) {
        return a * b;
    }
}

// Test arrow function with comment
const arrowFunction = (x) => {
    // Arrow function implementation
    return x * 2;
};

/*
 * Export the functions for testing
 */
module.exports = {
    greetPerson,
    calculateSum,
    complexCalculation,
    Calculator,
    arrowFunction
};

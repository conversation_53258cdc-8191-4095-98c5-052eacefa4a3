import { expect } from 'chai';
import sinon from 'sinon';
import { getParserForFile, getLanguageForFile, supportedLanguages } from './treeSitter';
import * as languageParser from '../../tree-sitter/languageParser';

describe('TreeSitter Utility Functions', () => {
    let getParserFromLanguageParserStub: sinon.SinonStub;
    let getCachedLanguageStub: sinon.SinonStub;
    let consoleDebugSpy: sinon.SinonSpy;

    beforeEach(() => {
        // 存根 languageParser 中的函数以便控制测试行为
        getParserFromLanguageParserStub = sinon.stub(languageParser, 'getParserForFile');
        getCachedLanguageStub = sinon.stub(languageParser, 'getCachedLanguage');
        
        // 监视 console.debug 以检查错误日志
        consoleDebugSpy = sinon.spy(console, 'debug');
    });

    afterEach(() => {
        // 恢复存根和间谍
        getParserFromLanguageParserStub.restore();
        getCachedLanguageStub.restore();
        consoleDebugSpy.restore();
    });

    describe('getParserForFile', () => {
        it('应成功返回指定文件的解析器', async () => {
            // 安排
            const filepath = 'test.js';
            const mockParser = {};
            getParserFromLanguageParserStub.resolves(mockParser);

            // 行动
            const parser = await getParserForFile(filepath);

            // 断言
            expect(parser).to.equal(mockParser);
            expect(getParserFromLanguageParserStub.calledOnceWith(filepath)).to.be.true;
        });

        it('当无法加载解析器时应返回 undefined 并记录调试信息', async () => {
            // 安排
            const filepath = 'test.unknown';
            getParserFromLanguageParserStub.resolves(undefined);

            // 行动
            const parser = await getParserForFile(filepath);

            // 断言
            expect(parser).to.be.undefined;
            expect(getParserFromLanguageParserStub.calledOnceWith(filepath)).to.be.true;
            expect(consoleDebugSpy.calledWithMatch('Unable to load language parser for file', filepath)).to.be.true;
        });

        it('当发生错误时应返回 undefined 并记录错误信息', async () => {
            // 安排
            const filepath = 'test.js';
            const error = new Error('加载解析器时出错');
            getParserFromLanguageParserStub.rejects(error);

            // 行动
            const parser = await getParserForFile(filepath);

            // 断言
            expect(parser).to.be.undefined;
            expect(getParserFromLanguageParserStub.calledOnceWith(filepath)).to.be.true;
            expect(consoleDebugSpy.calledWithMatch('Error loading language parser for file', filepath, error)).to.be.true;
        });
    });

    describe('getLanguageForFile', () => {
        it('应成功返回指定文件的语言对象', async () => {
            // 安排
            const filepath = 'test.js';
            const mockLanguage = {};
            const mockCachedLanguage = { language: mockLanguage, query: {} };
            getCachedLanguageStub.resolves(mockCachedLanguage);

            // 行动
            const language = await getLanguageForFile(filepath);

            // 断言
            expect(language).to.equal(mockLanguage);
            expect(getCachedLanguageStub.calledOnceWith('js')).to.be.true;
        });

        it('当无法找到语言时应返回 undefined 并记录调试信息', async () => {
            // 安排
            const filepath = 'test.unknown';
            getCachedLanguageStub.resolves(undefined);

            // 行动
            const language = await getLanguageForFile(filepath);

            // 断言
            expect(language).to.be.undefined;
            expect(getCachedLanguageStub.calledOnceWith('unknown')).to.be.true;
            expect(consoleDebugSpy.calledWithMatch('No language found for extension', 'unknown')).to.be.true;
        });

        it('当发生错误时应返回 undefined 并记录错误信息', async () => {
            // 安排
            const filepath = 'test.js';
            const error = new Error('加载语言时出错');
            getCachedLanguageStub.rejects(error);

            // 行动
            const language = await getLanguageForFile(filepath);

            // 断言
            expect(language).to.be.undefined;
            expect(getCachedLanguageStub.calledOnceWith('js')).to.be.true;
            expect(consoleDebugSpy.calledWithMatch('Error loading language for file', filepath, error)).to.be.true;
        });
    });
});

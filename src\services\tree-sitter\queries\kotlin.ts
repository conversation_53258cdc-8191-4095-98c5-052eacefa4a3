export default `
(class_declaration
  (type_identifier) @name.definition.class) @definition.class

(function_declaration
  (simple_identifier) @name.definition.function) @definition.function



(object_declaration
  (type_identifier) @name.definition.object) @definition.object

(property_declaration
  (simple_identifier) @name.definition.property) @definition.property



(enum_entry
  (simple_identifier) @name.definition.enum_entry) @definition.enum_entry



(annotation
  (type_identifier) @name.definition.annotation) @definition.annotation
`

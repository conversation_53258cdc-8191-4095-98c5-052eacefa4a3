// AIDIFF: Updated to align with continue/core/autocomplete/templating/AutocompleteTemplate.ts
// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts
// Fill in the middle prompts

import * as vscode from "vscode"
import { CompletionOptions } from "../types"
import { AutocompleteSnippet, AutocompleteSnippetType } from "./snippetTypes"
import { CodeContext, CodeContextDefinition } from "../ContextGatherer"
import { extractIntelligentContext } from "../utils/contextExtractor"

// AIDIFF: Updated interface to match continue/
export interface AutocompleteTemplate {
	compilePrefixSuffix?: (prefix: string, suffix: string) => [string, string]
	getSystemPrompt: () => string
	template: (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => Promise<string>
	completionOptions?: Partial<CompletionOptions>
}

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (gptAutocompleteTemplate)
// const gptAutocompleteTemplate: AutocompleteTemplate = {
// 	template: `\`\`\`
// {{{prefix}}}[BLANK]{{{suffix}}}
// \`\`\`
//
// Fill in the blank to complete the code block. Your response should include only the code to replace [BLANK], without surrounding backticks.`,
// 	completionOptions: { stop: ["\n"] },
// }

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (holeFillerTemplate)
export const holeFillerTemplate: AutocompleteTemplate = {
	getSystemPrompt: () => {
		// From https://github.com/VictorTaelin/AI-scripts
		const SYSTEM_MSG = `You are helping me fill in a placeholder marked as {{FILL_HERE}} in a code file.
Instructions:
- Do NOT  write code or document for any other lines that are not directly related to the placeholder.
- Replace {{FILL_HERE}} with exactly one block of content: either code or comment.
- If the placeholder is directly before a function, method, class, struct, impl block, or module, provide a "language doc" sytle comment that describes just for it.
- Do NOT write any code or comments for individual functions unless explicitly precedes by {{FILL_HERE}}.
- Do NOT add or repeat any existing code — especially the following code.
- Do NOT output any text outside of the <COMPLETION> tag.
`
		return SYSTEM_MSG
	},
	template: async (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => {
		// Use intelligent context extraction instead of full file content
		const extractedContext = await extractIntelligentContext(document, position, 100)

		// Insert the fill placeholder at the cursor position within the context
		const cursorOffsetInContext =
			extractedContext.contextCode.split("\n").slice(0, extractedContext.cursorLineInContext).join("\n").length +
			(extractedContext.cursorLineInContext > 0 ? 1 : 0) + // Add 1 for newline if not first line
			extractedContext.cursorCharInContext

		const currentFileWithFillPlaceholder =
			extractedContext.contextCode.slice(0, cursorOffsetInContext) +
			"{{FILL_HERE}}" +
			extractedContext.contextCode.slice(cursorOffsetInContext)

		const queryContextStrings: string[] = []

		const codeContextItems = codeContext.definitions
		if (codeContextItems && codeContextItems.length > 0) {
			// AIDIFF: Ensure item.name is correct, CodeContextDefinition has filepath
			const contextItemStrings = codeContextItems.map(
				(item: CodeContextDefinition) => `// File: ${item.filepath}\n${item.content}`,
			)
			queryContextStrings.push(`// Context from other parts of the project:\n${contextItemStrings.join("\n\n")}`)
		}

		if (snippets && snippets.length > 0) {
			const snippetStrings = snippets.map((snippet) => {
				let header = `// Some context: ${snippet.type})`
				if ("filepath" in snippet && (snippet as any).filepath) {
					header = `// Some context: ${snippet.type}) from: ${(snippet as any).filepath}`
				} else if (
					snippet.type === AutocompleteSnippetType.Clipboard &&
					"copiedAt" in snippet &&
					(snippet as any).copiedAt
				) {
					header = `// Some context: ${snippet.type}, copiedAt: ${(snippet as any).copiedAt})`
				}
				return `${header}\n${snippet.content}`
			})
			queryContextStrings.push(`// Relevant snippets:\n${snippetStrings.join("\n\n")}`)
		}

		// Add the current file with hole last, as it's the primary focus
		queryContextStrings.push(`// Current file content with hole:\n${currentFileWithFillPlaceholder}`)

		const queryContent = queryContextStrings.join("\n\n")

		const TASK =
			`complete the {{FILL_HERE}} part of the content above. ` +
			`Include the answer inside a <COMPLETION></COMPLETION> tag. Answer only with the CORRECT completion, and NOTHING ELSE. Do it now.`

		const userPrompt = `\n\n<QUERY>\n${queryContent}\n</QUERY>\nTASK: ${TASK}\n`
		return userPrompt
	},
	completionOptions: {
		stop: ["</COMPLETION>"],
	},
}

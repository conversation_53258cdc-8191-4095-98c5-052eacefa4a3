/**
 * Test script to verify the enhanced comment extraction functionality
 * in the contextExtractor.ts file
 */

const vscode = require('vscode');
const path = require('path');

// Import the enhanced extractIntelligentContext function
const { extractIntelligentContext } = require('./src/services/autocomplete/utils/contextExtractor');

async function testCommentExtraction() {
    console.log('🧪 Testing Enhanced Comment Extraction Functionality');
    console.log('='.repeat(60));

    // Enable debug mode for detailed logging
    process.env.DEBUG_CONTEXT_EXTRACTION = 'true';

    try {
        // Read the test file
        const testFilePath = path.join(__dirname, 'test-comment-extraction.js');
        const testFileUri = vscode.Uri.file(testFilePath);
        
        console.log(`📁 Opening test file: ${testFilePath}`);
        const document = await vscode.workspace.openTextDocument(testFileUri);
        
        const testCases = [
            {
                name: 'JSDoc Function - greetPerson',
                line: 11, // Inside the greetPerson function
                char: 10,
                expectedComments: ['JSDoc documentation', 'param', 'returns']
            },
            {
                name: 'Multi-line comment - calculateSum',
                line: 19, // Inside calculateSum function
                char: 5,
                expectedComments: ['Single line comment', 'multiple lines']
            },
            {
                name: 'Block comment - complexCalculation',
                line: 30, // Inside complexCalculation function
                char: 15,
                expectedComments: ['Multi-line block comment', 'detailed explanation']
            },
            {
                name: 'Class JSDoc - Calculator',
                line: 45, // Inside Calculator constructor
                char: 8,
                expectedComments: ['Class with JSDoc', '@class Calculator']
            },
            {
                name: 'Method JSDoc - add method',
                line: 55, // Inside add method
                char: 12,
                expectedComments: ['Adds two numbers', '@param', '@returns']
            }
        ];

        let passedTests = 0;
        const totalTests = testCases.length;

        for (let i = 0; i < testCases.length; i++) {
            const testCase = testCases[i];
            console.log(`\n📋 Test ${i + 1}/${totalTests}: ${testCase.name}`);
            console.log(`📍 Position: line ${testCase.line + 1}, char ${testCase.char + 1}`);

            try {
                const position = new vscode.Position(testCase.line, testCase.char);
                const result = await extractIntelligentContext(document, position, 50);

                console.log(`✅ Strategy: ${result.strategy}`);
                console.log(`📊 Context lines: ${result.contextCode.split('\n').length}`);
                console.log(`💬 Associated comments: ${result.associatedComments ? result.associatedComments.length : 0}`);

                if (result.associatedComments && result.associatedComments.length > 0) {
                    console.log('📝 Comments found:');
                    result.associatedComments.forEach((comment, idx) => {
                        const preview = comment.substring(0, 80).replace(/\n/g, ' ');
                        console.log(`   ${idx + 1}. ${preview}${comment.length > 80 ? '...' : ''}`);
                    });

                    // Check if expected comment patterns are found
                    const hasExpectedComments = testCase.expectedComments.some(expected =>
                        result.associatedComments.some(comment =>
                            comment.toLowerCase().includes(expected.toLowerCase())
                        )
                    );

                    if (hasExpectedComments) {
                        console.log('✅ Expected comment patterns found');
                        passedTests++;
                    } else {
                        console.log('❌ Expected comment patterns not found');
                        console.log(`   Expected patterns: ${testCase.expectedComments.join(', ')}`);
                    }
                } else {
                    console.log('❌ No comments extracted');
                }

                // Show context preview
                const contextLines = result.contextCode.split('\n');
                const previewLines = Math.min(5, contextLines.length);
                console.log(`📄 Context preview (first ${previewLines} lines):`);
                for (let j = 0; j < previewLines; j++) {
                    const marker = j === result.cursorLineInContext ? ' 👈' : '';
                    console.log(`   ${j + 1}: ${contextLines[j]}${marker}`);
                }

            } catch (error) {
                console.log(`❌ Test failed with error: ${error.message}`);
                console.log(`   Stack: ${error.stack}`);
            }
        }

        console.log(`\n🏁 Test Summary:`);
        console.log(`✅ Passed: ${passedTests}/${totalTests}`);
        console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
        console.log(`📊 Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

        if (passedTests === totalTests) {
            console.log('🎉 All comment extraction tests passed!');
        } else {
            console.log('⚠️  Some tests failed. Check the implementation.');
        }

    } catch (error) {
        console.error('❌ Test setup failed:', error);
    } finally {
        // Clean up debug mode
        delete process.env.DEBUG_CONTEXT_EXTRACTION;
    }
}

// Export the test function
module.exports = { testCommentExtraction };

// Run tests if this file is executed directly
if (require.main === module) {
    testCommentExtraction().catch(console.error);
}

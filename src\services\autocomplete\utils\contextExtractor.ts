import * as vscode from "vscode"
import { getParserForFile } from "../../tree-sitter/languageParser"
import type Parser from "web-tree-sitter"

export interface ExtractedContext {
	/** The relevant code context around the cursor */
	contextCode: string
	/** The line number where the cursor is positioned within the context */
	cursorLineInContext: number
	/** The character position where the cursor is positioned within the context */
	cursorCharInContext: number
	/** Whether the full file was used (fallback when tree-sitter fails) */
	usedFullFile: boolean
	/** The extraction strategy used */
	strategy: "meaningful-parent" | "inter-declaration" | "context-window" | "limited-fallback"
	/** Associated comments found for the context (JSDoc, function comments, etc.) */
	associatedComments?: string[]
}

/**
 * Extracts intelligent code context around the cursor position using tree-sitter.
 * This reduces prompt length by including only relevant code sections instead of the entire file.
 */
export async function extractIntelligentContext(
	document: vscode.TextDocument,
	position: vscode.Position,
	maxLines: number = 100,
): Promise<ExtractedContext> {
	const debugEnabled = process.env.NODE_ENV === "development" || process.env.DEBUG_CONTEXT_EXTRACTION === "true"

	if (debugEnabled) {
		console.log(`🧪 [Context Extraction] Starting extraction for ${document.uri.fsPath}`)
		console.log(`🧪 [Context Extraction] Cursor at line ${position.line + 1}, char ${position.character + 1}`)
	}

	try {
		const parser = await getParserForFile(document.uri.fsPath)
		if (!parser) {
			if (debugEnabled) {
				console.log("🧪 [Context Extraction] No parser available, using limited fallback")
			}
			return extractLimitedContext(document, position, maxLines)
		}

		const fileContent = document.getText()
		const ast = parser.parse(fileContent)

		if (!ast) {
			if (debugEnabled) {
				console.log("🧪 [Context Extraction] AST parsing failed, using limited fallback")
			}
			return extractLimitedContext(document, position, maxLines)
		}

		if (debugEnabled) {
			console.log(`🧪 [Context Extraction] AST parsed successfully, root node type: ${ast.rootNode.type}`)
			console.log(`🧪 [Context Extraction] Root node children count: ${ast.rootNode.children.length}`)
		}

		const cursorOffset = document.offsetAt(position)
		const contextResult = findRelevantContext(ast, cursorOffset, fileContent, maxLines)

		if (!contextResult) {
			if (debugEnabled) {
				console.log("🧪 [Context Extraction] No relevant context found, using limited fallback")
			}
			return extractLimitedContext(document, position, maxLines)
		}

		const contextCode = fileContent.slice(contextResult.start, contextResult.end)
		const contextStartPos = document.positionAt(contextResult.start)

		// Extract associated comments for the context
		const associatedComments = extractAssociatedComments(ast, contextResult, fileContent, debugEnabled)

		if (debugEnabled) {
			console.log(`🧪 [Context Extraction] Strategy: ${contextResult.strategy}`)
			console.log(`🧪 [Context Extraction] Context range: ${contextResult.start}-${contextResult.end}`)
			console.log(`🧪 [Context Extraction] Context lines: ${contextCode.split("\n").length}`)
			console.log(
				`🧪 [Context Extraction] Context start position: line ${contextStartPos.line + 1}, char ${contextStartPos.character + 1}`,
			)
			if (associatedComments.length > 0) {
				console.log(`🧪 [Context Extraction] Found ${associatedComments.length} associated comments`)
			}
		}

		return {
			contextCode,
			cursorLineInContext: position.line - contextStartPos.line,
			cursorCharInContext:
				position.line === contextStartPos.line ? position.character - contextStartPos.character : position.character,
			usedFullFile: false,
			strategy: contextResult.strategy,
			associatedComments: associatedComments.length > 0 ? associatedComments : undefined,
		}
	} catch (error) {
		console.warn("🌳 Context extraction failed, using limited fallback:", error)
		return extractLimitedContext(document, position, maxLines)
	}
}

/**
 * Finds the most relevant code context around the cursor using tree-sitter AST.
 */
function findRelevantContext(
	ast: Parser.Tree,
	cursorOffset: number,
	fileContent: string,
	maxLines: number,
): { start: number; end: number; strategy: "meaningful-parent" | "inter-declaration" | "context-window" } | null {
	const debugEnabled = process.env.NODE_ENV === "development" || process.env.DEBUG_CONTEXT_EXTRACTION === "true"

	if (debugEnabled) {
		console.log(`🧪 [findRelevantContext] Cursor offset: ${cursorOffset}`)
	}

	// Find the node that contains the cursor
	const cursorNode = findNodeAtOffset(ast.rootNode, cursorOffset)
	if (!cursorNode) {
		if (debugEnabled) {
			console.log("🧪 [findRelevantContext] No cursor node found")
		}
		return null
	}

	if (debugEnabled) {
		console.log(
			`🧪 [findRelevantContext] Cursor node type: ${cursorNode.type}, range: ${cursorNode.startIndex}-${cursorNode.endIndex}`,
		)
	}

	// Try to find a meaningful parent node (function, class, method, etc.)
	const contextNode = findMeaningfulParent(cursorNode)

	if (contextNode) {
		// Try to include preceding comments with the meaningful parent
		const expandedContext = expandContextWithComments(ast, contextNode, fileContent, maxLines, debugEnabled)

		const lines = fileContent.slice(0, expandedContext.end).split("\n")
		const nodeLines = lines.length - fileContent.slice(0, expandedContext.start).split("\n").length + 1

		if (debugEnabled) {
			console.log(
				`🧪 [findRelevantContext] Found meaningful parent: ${contextNode.type}, lines: ${nodeLines}, range: ${expandedContext.start}-${expandedContext.end}`,
			)
			if (expandedContext.start !== contextNode.startIndex) {
				console.log(`🧪 [findRelevantContext] Expanded context to include comments (original start: ${contextNode.startIndex})`)
			}
		}

		if (nodeLines <= maxLines) {
			if (debugEnabled) {
				console.log("🧪 [findRelevantContext] Using meaningful-parent strategy")
			}
			return {
				start: expandedContext.start,
				end: expandedContext.end,
				strategy: "meaningful-parent",
			}
		} else {
			if (debugEnabled) {
				console.log(`🧪 [findRelevantContext] Meaningful parent too large (${nodeLines} > ${maxLines} lines)`)
			}
		}
	} else {
		if (debugEnabled) {
			console.log("🧪 [findRelevantContext] No meaningful parent found")
		}
	}

	// Check if cursor is between top-level declarations (inter-function positioning)
	const interDeclarationContext = findInterDeclarationContext(ast.rootNode, cursorOffset, fileContent, maxLines)
	if (interDeclarationContext) {
		if (debugEnabled) {
			console.log(
				`🧪 [findRelevantContext] Using inter-declaration strategy, range: ${interDeclarationContext.start}-${interDeclarationContext.end}`,
			)
		}
		return {
			...interDeclarationContext,
			strategy: "inter-declaration",
		}
	}

	// If the meaningful parent is too large, use a window around the cursor
	if (debugEnabled) {
		console.log("🧪 [findRelevantContext] Using context-window strategy")
	}
	const windowContext = createContextWindow(cursorOffset, fileContent, maxLines)
	return {
		...windowContext,
		strategy: "context-window",
	}
}

/**
 * Finds the AST node that contains the given offset.
 */
function findNodeAtOffset(node: Parser.SyntaxNode, offset: number): Parser.SyntaxNode | null {
	if (offset < node.startIndex || offset > node.endIndex) {
		return null
	}

	// Check children first (depth-first search)
	for (const child of node.children) {
		const childResult = findNodeAtOffset(child, offset)
		if (childResult) {
			return childResult
		}
	}

	// If no child contains the offset, this node is the most specific
	return node
}

/**
 * Finds a meaningful parent node (function, class, method, etc.) that provides good context.
 */
function findMeaningfulParent(node: Parser.SyntaxNode): Parser.SyntaxNode | null {
	const meaningfulTypes = new Set([
		"function_declaration",
		"function_definition",
		"method_definition",
		"class_declaration",
		"class_definition",
		"interface_declaration",
		"type_alias_declaration",
		"enum_declaration",
		"struct_item",
		"impl_item",
		"trait_item",
		"module_item",
		"namespace_definition",
		"function_item",
		"arrow_function",
		"function_expression",
		"method_declaration",
		"constructor_declaration",
		"get_accessor",
		"set_accessor",
	])

	let current: Parser.SyntaxNode | null = node
	while (current) {
		if (meaningfulTypes.has(current.type)) {
			return current
		}
		current = current.parent
	}

	return null
}

/**
 * Detects if cursor is positioned between top-level declarations and extracts relevant context.
 * This handles cases where the cursor is in comments, whitespace, or other content between functions/classes.
 */
function findInterDeclarationContext(
	rootNode: Parser.SyntaxNode,
	cursorOffset: number,
	fileContent: string,
	maxLines: number,
): { start: number; end: number } | null {
	const debugEnabled = process.env.NODE_ENV === "development" || process.env.DEBUG_CONTEXT_EXTRACTION === "true"

	// Get all top-level declarations
	const topLevelDeclarations = getTopLevelDeclarations(rootNode)

	if (debugEnabled) {
		console.log(`🧪 [findInterDeclarationContext] Found ${topLevelDeclarations.length} top-level declarations`)
		topLevelDeclarations.forEach((decl, i) => {
			console.log(`🧪 [findInterDeclarationContext]   ${i + 1}. ${decl.type} (${decl.startIndex}-${decl.endIndex})`)
		})
	}

	if (topLevelDeclarations.length === 0) {
		if (debugEnabled) {
			console.log("🧪 [findInterDeclarationContext] No top-level declarations found")
		}
		return null
	}

	// Find the declarations that surround the cursor
	let precedingDeclaration: Parser.SyntaxNode | null = null
	let followingDeclaration: Parser.SyntaxNode | null = null

	for (const declaration of topLevelDeclarations) {
		if (declaration.endIndex <= cursorOffset) {
			precedingDeclaration = declaration
		} else if (declaration.startIndex > cursorOffset && !followingDeclaration) {
			followingDeclaration = declaration
			break
		}
	}

	if (debugEnabled) {
		console.log(
			`🧪 [findInterDeclarationContext] Preceding: ${precedingDeclaration?.type || "none"} (${precedingDeclaration?.startIndex || "N/A"}-${precedingDeclaration?.endIndex || "N/A"})`,
		)
		console.log(
			`🧪 [findInterDeclarationContext] Following: ${followingDeclaration?.type || "none"} (${followingDeclaration?.startIndex || "N/A"}-${followingDeclaration?.endIndex || "N/A"})`,
		)
	}

	// Check if cursor is actually between declarations (not inside one)
	const isInsideDeclaration = topLevelDeclarations.some(
		(decl) => cursorOffset >= decl.startIndex && cursorOffset <= decl.endIndex,
	)

	if (debugEnabled) {
		console.log(`🧪 [findInterDeclarationContext] Cursor inside declaration: ${isInsideDeclaration}`)
	}

	if (isInsideDeclaration) {
		return null // Cursor is inside a declaration, not between them
	}

	// Determine context boundaries
	let contextStart: number
	let contextEnd: number

	if (precedingDeclaration && followingDeclaration) {
		// Cursor between two declarations
		contextStart = precedingDeclaration.startIndex
		contextEnd = followingDeclaration.endIndex
	} else if (precedingDeclaration && !followingDeclaration) {
		// Cursor after last declaration (end of file)
		contextStart = precedingDeclaration.startIndex
		contextEnd = fileContent.length
	} else if (!precedingDeclaration && followingDeclaration) {
		// Cursor before first declaration (beginning of file)
		contextStart = 0
		contextEnd = followingDeclaration.endIndex
	} else {
		// No declarations found
		return null
	}

	// Check if the combined context exceeds maxLines
	const contextContent = fileContent.slice(contextStart, contextEnd)
	const contextLines = contextContent.split("\n").length

	if (contextLines > maxLines) {
		// Fall back to context window if too large
		return null
	}

	return {
		start: contextStart,
		end: contextEnd,
	}
}

/**
 * Gets all top-level declarations from the AST root node.
 */
function getTopLevelDeclarations(rootNode: Parser.SyntaxNode): Parser.SyntaxNode[] {
	const topLevelTypes = new Set([
		"function_declaration",
		"function_definition",
		"class_declaration",
		"class_definition",
		"interface_declaration",
		"type_alias_declaration",
		"enum_declaration",
		"struct_item",
		"impl_item",
		"trait_item",
		"module_item",
		"namespace_definition",
		"function_item",
		"variable_declaration",
		"lexical_declaration",
		"export_statement",
		"import_statement",
	])

	const declarations: Parser.SyntaxNode[] = []

	function collectDeclarations(node: Parser.SyntaxNode) {
		if (topLevelTypes.has(node.type)) {
			declarations.push(node)
		}

		// Only traverse direct children of root for top-level declarations
		if (node === rootNode) {
			for (const child of node.children) {
				collectDeclarations(child)
			}
		}
	}

	collectDeclarations(rootNode)

	// Sort by start position
	return declarations.sort((a, b) => a.startIndex - b.startIndex)
}

/**
 * Creates a context window around the cursor when tree-sitter analysis doesn't provide a good boundary.
 */
function createContextWindow(cursorOffset: number, fileContent: string, maxLines: number): { start: number; end: number } {
	const lines = fileContent.split("\n")
	const cursorLine = fileContent.slice(0, cursorOffset).split("\n").length - 1

	const halfLines = Math.floor(maxLines / 2)
	const startLine = Math.max(0, cursorLine - halfLines)
	const endLine = Math.min(lines.length - 1, cursorLine + halfLines)

	const startOffset = startLine === 0 ? 0 : lines.slice(0, startLine).join("\n").length + 1
	const endOffset = endLine === lines.length - 1 ? fileContent.length : lines.slice(0, endLine + 1).join("\n").length

	return { start: startOffset, end: endOffset }
}

/**
 * Fallback context extraction when tree-sitter is not available.
 * Uses a simple line-based approach with limited context.
 */
function extractLimitedContext(document: vscode.TextDocument, position: vscode.Position, maxLines: number): ExtractedContext {
	const totalLines = document.lineCount
	const currentLine = position.line

	// Calculate context window
	const halfLines = Math.floor(maxLines / 2)
	const startLine = Math.max(0, currentLine - halfLines)
	const endLine = Math.min(totalLines - 1, currentLine + halfLines)

	// Extract context
	const contextRange = new vscode.Range(
		new vscode.Position(startLine, 0),
		new vscode.Position(endLine, document.lineAt(endLine).text.length),
	)

	const contextCode = document.getText(contextRange)

	// Try to extract basic comments in fallback mode
	const associatedComments = extractBasicComments(contextCode)

	return {
		contextCode,
		cursorLineInContext: currentLine - startLine,
		cursorCharInContext: position.character,
		usedFullFile: startLine === 0 && endLine === totalLines - 1,
		strategy: "limited-fallback",
		associatedComments: associatedComments.length > 0 ? associatedComments : undefined,
	}
}

/**
 * Test function to validate context extraction with real VS Code documents
 * Call this function from the extension's activate function to run tests
 */
export async function testContextExtraction(): Promise<void> {
	console.log("🧪 [Test] Starting context extraction tests...")

	const testCases = [
		{
			name: "JS - Function inside",
			content: `function firstFunction(param1, param2) {
    const result = param1 + param2;
    if (result > 0) {
        return result * 2;
    }
    return 0;
}

// Comment between functions
function secondFunction() {
    return 'test';
}`,
			cursorLine: 2, // "    if (result > 0) {"
			cursorChar: 8,
			expectedStrategy: "meaningful-parent",
			language: "javascript",
		},
		{
			name: "JS - Between functions",
			content: `function firstFunction() {
    return 1;
}

// Comment between functions
// This should trigger inter-declaration

function secondFunction() {
    return 2;
}`,
			cursorLine: 5, // "// This should trigger inter-declaration"
			cursorChar: 10,
			expectedStrategy: "inter-declaration",
			language: "javascript",
		},
		{
			name: "TS - Interface inside",
			content: `interface TestInterface {
    id: number;
    name: string;
    active: boolean;
}

class TestClass {
    value: number = 0;
}`,
			cursorLine: 2, // "    name: string;"
			cursorChar: 8,
			expectedStrategy: "meaningful-parent",
			language: "typescript",
		},
		{
			name: "PY - Function inside",
			content: `def first_function(param1, param2):
    """Function docstring"""
    result = param1 + param2
    if result > 0:
        return result * 2
    return 0

def second_function():
    return "test"`,
			cursorLine: 2, // "    result = param1 + param2"
			cursorChar: 10,
			expectedStrategy: "meaningful-parent",
			language: "python",
		},
	]

	// Enable debug logging for tests
	process.env.DEBUG_CONTEXT_EXTRACTION = "true"

	let passedTests = 0
	let totalTests = testCases.length

	for (let i = 0; i < testCases.length; i++) {
		const testCase = testCases[i]
		console.log(`\n📋 [Test ${i + 1}/${totalTests}] ${testCase.name}`)
		console.log(`📝 Expected strategy: ${testCase.expectedStrategy}`)
		console.log(`📍 Cursor position: line ${testCase.cursorLine + 1}, char ${testCase.cursorChar + 1}`)

		try {
			// Create a temporary document with proper file extension
			const fileExtension =
				testCase.language === "javascript"
					? ".js"
					: testCase.language === "typescript"
						? ".ts"
						: testCase.language === "python"
							? ".py"
							: ".txt"

			const doc = await vscode.workspace.openTextDocument(
				vscode.Uri.parse(`untitled:test${fileExtension}`).with({
					scheme: "untitled",
					path: `test${fileExtension}`,
				}),
			)

			// Set the document content
			const edit = new vscode.WorkspaceEdit()
			edit.replace(doc.uri, new vscode.Range(0, 0, doc.lineCount, 0), testCase.content)
			await vscode.workspace.applyEdit(edit)

			const position = new vscode.Position(testCase.cursorLine, testCase.cursorChar)
			const result = await extractIntelligentContext(doc, position, 20)

			console.log(`✅ Result strategy: ${result.strategy}`)
			console.log(`📊 Context lines: ${result.contextCode.split("\n").length}`)
			console.log(`🎯 Cursor in context: line ${result.cursorLineInContext + 1}, char ${result.cursorCharInContext + 1}`)
			console.log(`📄 Used full file: ${result.usedFullFile}`)

			// Show first few lines of context
			const contextLines = result.contextCode.split("\n")
			const showLines = Math.min(5, contextLines.length)
			console.log(`📄 Context preview (first ${showLines} lines):`)
			for (let j = 0; j < showLines; j++) {
				const marker = j === result.cursorLineInContext ? " 👈" : ""
				console.log(`   ${j + 1}: ${contextLines[j]}${marker}`)
			}

			// Check if strategy matches expected
			const strategyMatches = result.strategy === testCase.expectedStrategy
			if (strategyMatches) {
				console.log(`✅ Strategy matches expected: ${testCase.expectedStrategy}`)
				passedTests++
			} else {
				console.log(`❌ Strategy mismatch! Expected: ${testCase.expectedStrategy}, Got: ${result.strategy}`)
			}
		} catch (error) {
			console.log(`❌ Test failed with error: ${error.message}`)
			console.log(`   Stack: ${error.stack}`)
		}
	}

	console.log(`\n🏁 Test Summary:`)
	console.log(`✅ Passed: ${passedTests}/${totalTests}`)
	console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`)
	console.log(`📊 Success rate: ${Math.round((passedTests / totalTests) * 100)}%`)

	// Reset debug logging
	delete process.env.DEBUG_CONTEXT_EXTRACTION

	if (passedTests === totalTests) {
		console.log("🎉 All context extraction tests passed!")
	} else {
		console.log("⚠️  Some tests failed. Check the implementation.")
	}
}

/**
 * Simple test function that directly tests parser functionality
 */
export async function testParserFunctionality(): Promise<void> {
	console.log("🧪 [Parser Test] Starting parser functionality tests...")

	// Test parser availability
	const { getCachedParser, getParserForFile } = await import("../../tree-sitter/languageParser")

	const jsParser = getCachedParser("js")
	const tsParser = getCachedParser("ts")
	const pyParser = getCachedParser("py")

	console.log(`🧪 [Parser Test] JS parser loaded: ${!!jsParser}`)
	console.log(`🧪 [Parser Test] TS parser loaded: ${!!tsParser}`)
	console.log(`🧪 [Parser Test] PY parser loaded: ${!!pyParser}`)

	// Test getParserForFile function
	const testFiles = [
		{ path: "test.js", expected: true },
		{ path: "test.ts", expected: true },
		{ path: "test.py", expected: true },
		{ path: "test.txt", expected: false },
	]

	for (const testFile of testFiles) {
		const parser = await getParserForFile(testFile.path)
		const hasParser = !!parser
		const status = hasParser === testFile.expected ? "✅" : "❌"
		console.log(`${status} [Parser Test] ${testFile.path}: ${hasParser ? "parser available" : "no parser"}`)
	}

	// Test basic parsing
	if (jsParser) {
		try {
			const testCode = `function test() { return 42; }`
			// Use the parser property from the cached parser
			const tree = jsParser.parser.parse(testCode)

			console.log(`✅ [Parser Test] JS parsing works: ${tree.rootNode.type}`)
			console.log(`✅ [Parser Test] JS root children: ${tree.rootNode.children.length}`)
		} catch (error) {
			console.log(`❌ [Parser Test] JS parsing failed: ${error.message}`)
		}
	}

	console.log("🏁 [Parser Test] Parser functionality test completed")
}

/**
 * Extracts comments associated with the given context range.
 * This includes JSDoc comments, function comments, and other relevant documentation.
 */
function extractAssociatedComments(
	ast: Parser.Tree,
	contextResult: { start: number; end: number; strategy: string },
	fileContent: string,
	debugEnabled: boolean = false,
): string[] {
	const comments: string[] = []

	try {
		// Find all comment nodes in the AST
		const commentNodes = findCommentNodes(ast.rootNode)

		if (debugEnabled) {
			console.log(`🧪 [Comment Extraction] Found ${commentNodes.length} comment nodes in AST`)
		}

		// Filter comments that are relevant to the context
		const relevantComments = commentNodes.filter((commentNode) => {
			// Include comments that are:
			// 1. Just before the context (likely function/class documentation)
			// 2. Within the context range
			// 3. Immediately preceding meaningful declarations

			const commentEnd = commentNode.endIndex
			const commentStart = commentNode.startIndex

			// Comment is within the context
			if (commentStart >= contextResult.start && commentEnd <= contextResult.end) {
				return true
			}

			// Comment is just before the context (likely documentation)
			// Allow some whitespace between comment and context
			const gapBetweenCommentAndContext = contextResult.start - commentEnd
			if (gapBetweenCommentAndContext >= 0 && gapBetweenCommentAndContext <= 200) {
				// Check if there's only whitespace between comment and context
				const textBetween = fileContent.slice(commentEnd, contextResult.start)
				if (/^\s*$/.test(textBetween)) {
					return true
				}
			}

			return false
		})

		if (debugEnabled) {
			console.log(`🧪 [Comment Extraction] Found ${relevantComments.length} relevant comments`)
		}

		// Extract and clean comment text
		for (const commentNode of relevantComments) {
			const commentText = commentNode.text
			const cleanedComment = cleanCommentText(commentText)

			if (cleanedComment.trim()) {
				comments.push(cleanedComment)

				if (debugEnabled) {
					console.log(`🧪 [Comment Extraction] Added comment: ${cleanedComment.substring(0, 50)}...`)
				}
			}
		}
	} catch (error) {
		if (debugEnabled) {
			console.warn("🧪 [Comment Extraction] Error extracting comments:", error)
		}
	}

	return comments
}

/**
 * Recursively finds all comment nodes in the AST.
 */
function findCommentNodes(node: Parser.SyntaxNode): Parser.SyntaxNode[] {
	const commentNodes: Parser.SyntaxNode[] = []

	// Common comment node types across different languages
	const commentTypes = new Set([
		"comment",
		"line_comment",
		"block_comment",
		"documentation_comment",
		"doc_comment",
		"multiline_comment",
		"single_line_comment",
	])

	function traverse(currentNode: Parser.SyntaxNode) {
		// Check if current node is a comment
		if (commentTypes.has(currentNode.type)) {
			commentNodes.push(currentNode)
		}

		// Recursively check children
		for (const child of currentNode.children) {
			traverse(child)
		}
	}

	traverse(node)
	return commentNodes
}

/**
 * Cleans comment text by removing comment markers and normalizing whitespace.
 */
function cleanCommentText(commentText: string): string {
	let cleaned = commentText

	// Remove common comment markers
	cleaned = cleaned
		// Remove /* */ style comments
		.replace(/^\/\*\*?/, "")
		.replace(/\*\/$/, "")
		// Remove // style comments
		.replace(/^\/\/+\s?/gm, "")
		// Remove # style comments (Python, Ruby, etc.)
		.replace(/^#+\s?/gm, "")
		// Remove leading * from JSDoc style comments
		.replace(/^\s*\*\s?/gm, "")
		// Remove HTML comment markers
		.replace(/^<!--/, "")
		.replace(/-->$/, "")

	// Normalize whitespace
	cleaned = cleaned
		.split("\n")
		.map((line) => line.trim())
		.filter((line) => line.length > 0)
		.join("\n")
		.trim()

	return cleaned
}

/**
 * Expands the context to include preceding comments (like JSDoc) that are associated with the node.
 */
function expandContextWithComments(
	ast: Parser.Tree,
	node: Parser.SyntaxNode,
	fileContent: string,
	maxLines: number,
	debugEnabled: boolean = false,
): { start: number; end: number } {
	let contextStart = node.startIndex
	const contextEnd = node.endIndex

	try {
		// Find comment nodes that precede this node
		const commentNodes = findCommentNodes(ast.rootNode)

		// Look for comments that are just before this node
		const precedingComments = commentNodes.filter((commentNode) => {
			const commentEnd = commentNode.endIndex
			const gapBetweenCommentAndNode = contextStart - commentEnd

			// Comment should end before the node starts
			if (commentEnd >= contextStart) {
				return false
			}

			// Allow reasonable gap (whitespace) between comment and node
			if (gapBetweenCommentAndNode > 300) {
				return false
			}

			// Check if there's only whitespace between comment and node
			const textBetween = fileContent.slice(commentEnd, contextStart)
			return /^\s*$/.test(textBetween)
		})

		// Sort by position (closest to node first)
		precedingComments.sort((a, b) => b.startIndex - a.startIndex)

		if (debugEnabled && precedingComments.length > 0) {
			console.log(`🧪 [expandContextWithComments] Found ${precedingComments.length} preceding comments`)
		}

		// Include consecutive preceding comments
		for (const commentNode of precedingComments) {
			const potentialStart = commentNode.startIndex

			// Check if including this comment would exceed maxLines
			const lines = fileContent.slice(0, contextEnd).split("\n")
			const potentialLines = lines.length - fileContent.slice(0, potentialStart).split("\n").length + 1

			if (potentialLines <= maxLines) {
				contextStart = potentialStart
				if (debugEnabled) {
					console.log(`🧪 [expandContextWithComments] Including comment at ${potentialStart}-${commentNode.endIndex}`)
				}
			} else {
				if (debugEnabled) {
					console.log(`🧪 [expandContextWithComments] Skipping comment - would exceed maxLines (${potentialLines} > ${maxLines})`)
				}
				break
			}
		}
	} catch (error) {
		if (debugEnabled) {
			console.warn("🧪 [expandContextWithComments] Error expanding context:", error)
		}
	}

	return { start: contextStart, end: contextEnd }
}

/**
 * Extracts basic comments from text using regex patterns (fallback when tree-sitter is not available).
 */
function extractBasicComments(text: string): string[] {
	const comments: string[] = []

	try {
		const lines = text.split("\n")
		let currentComment: string[] = []
		let inBlockComment = false

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i]
			const trimmedLine = line.trim()

			// Handle block comments (/* */)
			if (trimmedLine.includes("/*")) {
				inBlockComment = true
				currentComment = [line]

				// Check if block comment ends on same line
				if (trimmedLine.includes("*/")) {
					inBlockComment = false
					const cleanedComment = cleanCommentText(currentComment.join("\n"))
					if (cleanedComment.trim()) {
						comments.push(cleanedComment)
					}
					currentComment = []
				}
				continue
			}

			if (inBlockComment) {
				currentComment.push(line)
				if (trimmedLine.includes("*/")) {
					inBlockComment = false
					const cleanedComment = cleanCommentText(currentComment.join("\n"))
					if (cleanedComment.trim()) {
						comments.push(cleanedComment)
					}
					currentComment = []
				}
				continue
			}

			// Handle single-line comments
			if (trimmedLine.startsWith("//") || trimmedLine.startsWith("#")) {
				// Check if this is part of a multi-line comment block
				if (currentComment.length === 0 ||
					(i > 0 && (lines[i - 1].trim().startsWith("//") || lines[i - 1].trim().startsWith("#")))) {
					currentComment.push(line)
				} else {
					// Finish previous comment block if any
					if (currentComment.length > 0) {
						const cleanedComment = cleanCommentText(currentComment.join("\n"))
						if (cleanedComment.trim()) {
							comments.push(cleanedComment)
						}
					}
					currentComment = [line]
				}

				// Check if next line is also a comment to continue the block
				if (i === lines.length - 1 ||
					(!lines[i + 1].trim().startsWith("//") && !lines[i + 1].trim().startsWith("#"))) {
					const cleanedComment = cleanCommentText(currentComment.join("\n"))
					if (cleanedComment.trim()) {
						comments.push(cleanedComment)
					}
					currentComment = []
				}
				continue
			}

			// If we hit a non-comment line and have accumulated comments, finish the block
			if (currentComment.length > 0 && !inBlockComment) {
				const cleanedComment = cleanCommentText(currentComment.join("\n"))
				if (cleanedComment.trim()) {
					comments.push(cleanedComment)
				}
				currentComment = []
			}
		}

		// Handle any remaining comment block
		if (currentComment.length > 0) {
			const cleanedComment = cleanCommentText(currentComment.join("\n"))
			if (cleanedComment.trim()) {
				comments.push(cleanedComment)
			}
		}
	} catch (error) {
		console.warn("Error extracting basic comments:", error)
	}

	return comments
}

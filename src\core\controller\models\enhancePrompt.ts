import { Controller } from ".."
import { StringRequest } from "../../../shared/proto/common"
import { buildApi<PERSON><PERSON><PERSON> } from "@api/index"
import { getAllExtensionState } from "../../storage/state"

/**
 * Enhances a prompt using the configured API without creating a full Cline instance or task history.
 * This is a lightweight alternative that uses the API's messaging functionality to improve the prompt based on context.
 * @param controller The controller instance
 * @param request The request containing the prompt text to enhance
 * @returns StringRequest with the enhanced prompt text
 */
export async function enhancePrompt(controller: Controller, request: StringRequest): Promise<StringRequest> {
	if (!request.value) {
		throw new Error("No prompt text provided")
	}

	const { apiConfiguration } = await getAllExtensionState(controller.context)
	if (!apiConfiguration || !apiConfiguration.apiProvider) {
		throw new Error("No valid API configuration provided")
	}

	const handler = buildApiHandler(apiConfiguration)

	// System prompt to guide the AI in enhancing the prompt
	const systemPrompt =
		"You are an expert in crafting effective prompts for AI models. Your task is to enhance the user's input prompt to be more specific, clear, and detailed while maintaining the original intent. Provide the enhanced prompt as your response."

	// User message with the original prompt
	const userMessage = {
		role: "user" as const,
		content: `Please enhance the following prompt:\n\n"${request.value}"`,
	}

	// Use the createMessage method to get the enhanced prompt
	const responseStream = handler.createMessage(systemPrompt, [userMessage])

	// Process the stream to get the full response
	let enhancedPrompt = ""
	for await (const chunk of responseStream) {
		if (chunk.type === "text" && chunk.text) {
			enhancedPrompt += chunk.text
		}
	}

	if (!enhancedPrompt) {
		enhancedPrompt = "Enhanced prompt could not be generated."
	}

	return StringRequest.create({ value: enhancedPrompt })
}
